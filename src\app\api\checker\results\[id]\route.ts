import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { testResults, candidates } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const resultId = params.id;

    // Get test result with candidate info
    const result = await db
      .select({
        id: testResults.id,
        candidateId: testResults.candidateId,
        listeningScore: testResults.listeningScore,
        listeningBandScore: testResults.listeningBandScore,
        readingScore: testResults.readingScore,
        readingBandScore: testResults.readingBandScore,
        writingTask1Score: testResults.writingTask1Score,
        writingTask2Score: testResults.writingTask2Score,
        writingBandScore: testResults.writingBandScore,
        speakingFluencyScore: testResults.speakingFluencyScore,
        speakingLexicalScore: testResults.speakingLexicalScore,
        speakingGrammarScore: testResults.speakingGrammarScore,
        speakingPronunciationScore: testResults.speakingPronunciationScore,
        speakingBandScore: testResults.speakingBandScore,
        overallBandScore: testResults.overallBandScore,
        status: testResults.status,
        certificateGenerated: testResults.certificateGenerated,
        createdAt: testResults.createdAt,
        updatedAt: testResults.updatedAt,
        candidate: {
          fullName: candidates.fullName,
          email: candidates.email,
          phoneNumber: candidates.phoneNumber,
          passportNumber: candidates.passportNumber,
          nationality: candidates.nationality,
          testDate: candidates.testDate,
          testCenter: candidates.testCenter,
          photoUrl: candidates.photoUrl,
        },
      })
      .from(testResults)
      .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        { error: 'Test result not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(result[0]);
  } catch (error) {
    console.error('Error fetching test result:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const resultId = params.id;
    const data = await request.json();

    // Check if result exists and user has permission to edit
    const existingResult = await db
      .select()
      .from(testResults)
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (!existingResult.length) {
      return NextResponse.json(
        { error: 'Test result not found' },
        { status: 404 }
      );
    }

    // Convert string values to numbers where needed
    const processedData = {
      // Listening scores
      listeningScore: data.listeningScore ? parseFloat(data.listeningScore) : null,
      listeningBandScore: data.listeningBandScore ? parseFloat(data.listeningBandScore) : null,
      
      // Reading scores
      readingScore: data.readingScore ? parseFloat(data.readingScore) : null,
      readingBandScore: data.readingBandScore ? parseFloat(data.readingBandScore) : null,
      
      // Writing scores
      writingTask1Score: data.writingTask1Score ? parseFloat(data.writingTask1Score) : null,
      writingTask2Score: data.writingTask2Score ? parseFloat(data.writingTask2Score) : null,
      writingBandScore: data.writingBandScore ? parseFloat(data.writingBandScore) : null,
      
      // Speaking scores
      speakingFluencyScore: data.speakingFluencyScore ? parseFloat(data.speakingFluencyScore) : null,
      speakingLexicalScore: data.speakingLexicalScore ? parseFloat(data.speakingLexicalScore) : null,
      speakingGrammarScore: data.speakingGrammarScore ? parseFloat(data.speakingGrammarScore) : null,
      speakingPronunciationScore: data.speakingPronunciationScore ? parseFloat(data.speakingPronunciationScore) : null,
      speakingBandScore: data.speakingBandScore ? parseFloat(data.speakingBandScore) : null,
      
      // Overall score
      overallBandScore: data.overallBandScore ? parseFloat(data.overallBandScore) : null,
      
      // Status
      status: data.status || existingResult[0].status,
      
      // Update timestamp
      updatedAt: new Date(),
    };

    // Update test result
    const updatedResult = await db
      .update(testResults)
      .set(processedData)
      .where(eq(testResults.id, resultId))
      .returning();

    return NextResponse.json(updatedResult[0]);
  } catch (error) {
    console.error('Error updating test result:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
