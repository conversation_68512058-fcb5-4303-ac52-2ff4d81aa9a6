'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Search, 
  ClipboardList, 
  BarChart3, 
  Clock,
  CheckCircle,
  AlertCircle,
  Brain,
  FileText
} from 'lucide-react';

interface CheckerStats {
  totalResultsEntered: number;
  pendingResults: number;
  completedResults: number;
  recentResults: any[];
}

export default function CheckerDashboard() {
  const [stats, setStats] = useState<CheckerStats>({
    totalResultsEntered: 0,
    pendingResults: 0,
    completedResults: 0,
    recentResults: [],
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchCheckerStats();
  }, []);

  const fetchCheckerStats = async () => {
    try {
      const response = await fetch('/api/checker/dashboard');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error fetching checker stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const statCards = [
    {
      name: 'Results Entered',
      value: stats.totalResultsEntered,
      icon: ClipboardList,
      color: 'bg-blue-500',
      href: '/dashboard/results/list',
    },
    {
      name: 'Pending Review',
      value: stats.pendingResults,
      icon: Clock,
      color: 'bg-yellow-500',
      href: '/dashboard/results/list?status=pending',
    },
    {
      name: 'Completed',
      value: stats.completedResults,
      icon: CheckCircle,
      color: 'bg-green-500',
      href: '/dashboard/results/list?status=completed',
    },
  ];

  const quickActions = [
    {
      name: 'Search Candidates',
      description: 'Find candidates to enter results',
      href: '/dashboard/search',
      icon: Search,
      color: 'bg-blue-600 hover:bg-blue-700',
    },
    {
      name: 'Enter Test Results',
      description: 'Add new test scores',
      href: '/dashboard/results',
      icon: ClipboardList,
      color: 'bg-green-600 hover:bg-green-700',
    },
    {
      name: 'View All Results',
      description: 'Browse entered results',
      href: '/dashboard/results/list',
      icon: BarChart3,
      color: 'bg-purple-600 hover:bg-purple-700',
    },
    {
      name: 'AI Feedback',
      description: 'Generate AI feedback',
      href: '/dashboard/feedback',
      icon: Brain,
      color: 'bg-indigo-600 hover:bg-indigo-700',
    },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Test Checker Dashboard</h1>
        <p className="text-gray-600">Manage IELTS test results and candidate information</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {statCards.map((stat) => {
          const Icon = stat.icon;
          return (
            <Link
              key={stat.name}
              href={stat.href}
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`${stat.color} p-3 rounded-md`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {stat.name}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stat.value.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action) => {
            const Icon = action.icon;
            return (
              <Link
                key={action.name}
                href={action.href}
                className={`${action.color} text-white p-6 rounded-lg shadow hover:shadow-md transition-all`}
              >
                <div className="flex items-center">
                  <Icon className="h-8 w-8 mr-4" />
                  <div>
                    <h3 className="font-medium">{action.name}</h3>
                    <p className="text-sm opacity-90">{action.description}</p>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Recent Results */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Test Results</h3>
        </div>
        <div className="p-6">
          {stats.recentResults.length > 0 ? (
            <div className="space-y-4">
              {stats.recentResults.slice(0, 5).map((result) => (
                <div key={result.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      {result.status === 'completed' ? (
                        <CheckCircle className="h-8 w-8 text-green-500" />
                      ) : result.status === 'pending' ? (
                        <Clock className="h-8 w-8 text-yellow-500" />
                      ) : (
                        <AlertCircle className="h-8 w-8 text-red-500" />
                      )}
                    </div>
                    <div className="ml-4">
                      <p className="font-medium text-gray-900">{result.candidate?.fullName}</p>
                      <p className="text-sm text-gray-500">
                        Overall Band: {result.overallBandScore || 'Pending'}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">
                      {new Date(result.createdAt).toLocaleDateString()}
                    </p>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      result.status === 'completed' 
                        ? 'bg-green-100 text-green-800'
                        : result.status === 'pending'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {result.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No recent results</p>
              <Link
                href="/dashboard/results"
                className="mt-2 inline-flex items-center text-blue-600 hover:text-blue-700"
              >
                Enter your first result →
              </Link>
            </div>
          )}
          
          {stats.recentResults.length > 0 && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <Link
                href="/dashboard/results/list"
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                View all results →
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* Tips and Guidelines */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-blue-900 mb-4">Test Checker Guidelines</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
          <div>
            <h4 className="font-medium mb-2">Scoring Guidelines:</h4>
            <ul className="space-y-1">
              <li>• Listening: 0-40 raw score → 1-9 band score</li>
              <li>• Reading: 0-40 raw score → 1-9 band score</li>
              <li>• Writing: Direct band scores (1-9)</li>
              <li>• Speaking: Direct band scores (1-9)</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">Quality Assurance:</h4>
            <ul className="space-y-1">
              <li>• Double-check all scores before submission</li>
              <li>• Ensure candidate details match test papers</li>
              <li>• Generate AI feedback for improvement areas</li>
              <li>• Mark results as completed when verified</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
