import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { testResults, candidates } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // Validate required fields
    if (!data.candidateId) {
      return NextResponse.json(
        { error: 'Candidate ID is required' },
        { status: 400 }
      );
    }

    // Check if candidate exists
    const candidate = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, data.candidateId))
      .limit(1);

    if (!candidate.length) {
      return NextResponse.json(
        { error: 'Candidate not found' },
        { status: 404 }
      );
    }

    // Check if results already exist for this candidate
    const existingResults = await db
      .select()
      .from(testResults)
      .where(eq(testResults.candidateId, data.candidateId))
      .limit(1);

    if (existingResults.length > 0) {
      return NextResponse.json(
        { error: 'Test results already exist for this candidate' },
        { status: 409 }
      );
    }

    // Convert string values to numbers where needed
    const processedData = {
      candidateId: data.candidateId,
      
      // Listening scores
      listeningScore: data.listeningScore ? parseFloat(data.listeningScore) : null,
      listeningBandScore: data.listeningBandScore ? parseFloat(data.listeningBandScore) : null,
      
      // Reading scores
      readingScore: data.readingScore ? parseFloat(data.readingScore) : null,
      readingBandScore: data.readingBandScore ? parseFloat(data.readingBandScore) : null,
      
      // Writing scores
      writingTask1Score: data.writingTask1Score ? parseFloat(data.writingTask1Score) : null,
      writingTask2Score: data.writingTask2Score ? parseFloat(data.writingTask2Score) : null,
      writingBandScore: data.writingBandScore ? parseFloat(data.writingBandScore) : null,
      
      // Speaking scores
      speakingFluencyScore: data.speakingFluencyScore ? parseFloat(data.speakingFluencyScore) : null,
      speakingLexicalScore: data.speakingLexicalScore ? parseFloat(data.speakingLexicalScore) : null,
      speakingGrammarScore: data.speakingGrammarScore ? parseFloat(data.speakingGrammarScore) : null,
      speakingPronunciationScore: data.speakingPronunciationScore ? parseFloat(data.speakingPronunciationScore) : null,
      speakingBandScore: data.speakingBandScore ? parseFloat(data.speakingBandScore) : null,
      
      // Overall score
      overallBandScore: data.overallBandScore ? parseFloat(data.overallBandScore) : null,
      
      // Metadata
      status: 'pending' as const,
      enteredBy: session.user?.id,
    };

    // Create test result
    const newResult = await db
      .insert(testResults)
      .values(processedData)
      .returning();

    return NextResponse.json(newResult[0], { status: 201 });
  } catch (error) {
    console.error('Error creating test result:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');

    const offset = (page - 1) * limit;
    const userId = session.user?.id;

    // Build where conditions
    let whereConditions = eq(testResults.enteredBy, userId);
    if (status) {
      whereConditions = eq(testResults.enteredBy, userId);
      // Add status filter if needed
    }

    // Get results with candidate info
    const results = await db
      .select({
        id: testResults.id,
        candidateId: testResults.candidateId,
        listeningBandScore: testResults.listeningBandScore,
        readingBandScore: testResults.readingBandScore,
        writingBandScore: testResults.writingBandScore,
        speakingBandScore: testResults.speakingBandScore,
        overallBandScore: testResults.overallBandScore,
        status: testResults.status,
        createdAt: testResults.createdAt,
        candidate: {
          fullName: candidates.fullName,
          passportNumber: candidates.passportNumber,
          testDate: candidates.testDate,
        },
      })
      .from(testResults)
      .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
      .where(whereConditions)
      .limit(limit)
      .offset(offset)
      .orderBy(testResults.createdAt);

    return NextResponse.json({
      results,
      page,
      limit,
    });
  } catch (error) {
    console.error('Error fetching test results:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
