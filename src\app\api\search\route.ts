import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { candidates, testResults } from '@/lib/db/schema';
import { eq, ilike, or } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const { query, searchType } = await request.json();

    if (!query || !searchType) {
      return NextResponse.json(
        { error: 'Query and search type are required' },
        { status: 400 }
      );
    }

    let results;

    switch (searchType) {
      case 'name':
        results = await db
          .select({
            id: testResults.id,
            listeningBandScore: testResults.listeningBandScore,
            readingBandScore: testResults.readingBandScore,
            writingBandScore: testResults.writingBandScore,
            speakingBandScore: testResults.speakingBandScore,
            overallBandScore: testResults.overallBandScore,
            certificateGenerated: testResults.certificateGenerated,
            candidate: {
              fullName: candidates.fullName,
              testDate: candidates.testDate,
              testCenter: candidates.testCenter,
            },
          })
          .from(testResults)
          .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
          .where(ilike(candidates.fullName, `%${query}%`));
        break;

      case 'email':
        results = await db
          .select({
            id: testResults.id,
            listeningBandScore: testResults.listeningBandScore,
            readingBandScore: testResults.readingBandScore,
            writingBandScore: testResults.writingBandScore,
            speakingBandScore: testResults.speakingBandScore,
            overallBandScore: testResults.overallBandScore,
            certificateGenerated: testResults.certificateGenerated,
            candidate: {
              fullName: candidates.fullName,
              testDate: candidates.testDate,
              testCenter: candidates.testCenter,
            },
          })
          .from(testResults)
          .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
          .where(eq(candidates.email, query));
        break;

      case 'passport':
        results = await db
          .select({
            id: testResults.id,
            listeningBandScore: testResults.listeningBandScore,
            readingBandScore: testResults.readingBandScore,
            writingBandScore: testResults.writingBandScore,
            speakingBandScore: testResults.speakingBandScore,
            overallBandScore: testResults.overallBandScore,
            certificateGenerated: testResults.certificateGenerated,
            candidate: {
              fullName: candidates.fullName,
              testDate: candidates.testDate,
              testCenter: candidates.testCenter,
            },
          })
          .from(testResults)
          .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
          .where(eq(candidates.passportNumber, query));
        break;

      case 'certificate':
        results = await db
          .select({
            id: testResults.id,
            listeningBandScore: testResults.listeningBandScore,
            readingBandScore: testResults.readingBandScore,
            writingBandScore: testResults.writingBandScore,
            speakingBandScore: testResults.speakingBandScore,
            overallBandScore: testResults.overallBandScore,
            certificateGenerated: testResults.certificateGenerated,
            candidate: {
              fullName: candidates.fullName,
              testDate: candidates.testDate,
              testCenter: candidates.testCenter,
            },
          })
          .from(testResults)
          .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
          .where(eq(testResults.id, query));
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid search type' },
          { status: 400 }
        );
    }

    return NextResponse.json(results);
  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
