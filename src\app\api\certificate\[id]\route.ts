import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { testResults, candidates } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { generateCertificate } from '@/lib/certificate-generator';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const resultId = params.id;

    // Get test result with candidate info
    const result = await db
      .select({
        testResult: testResults,
        candidate: candidates,
      })
      .from(testResults)
      .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        { error: 'Test result not found' },
        { status: 404 }
      );
    }

    const { testResult, candidate } = result[0];

    // Check if result is completed
    if (testResult.status !== 'completed' && testResult.status !== 'verified') {
      return NextResponse.json(
        { error: 'Certificate can only be generated for completed results' },
        { status: 400 }
      );
    }

    // Check if all required scores are available
    if (!testResult.overallBandScore) {
      return NextResponse.json(
        { error: 'Overall band score is required for certificate generation' },
        { status: 400 }
      );
    }

    // Generate the certificate PDF
    const pdfBase64 = await generateCertificate(testResult, candidate);

    // Convert base64 to buffer
    const pdfBuffer = Buffer.from(pdfBase64.split(',')[1], 'base64');

    // Update the test result to mark certificate as generated
    await db
      .update(testResults)
      .set({ 
        certificateGenerated: true,
        updatedAt: new Date()
      })
      .where(eq(testResults.id, resultId));

    // Return the PDF as a downloadable file
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="IELTS_Certificate_${candidate.fullName.replace(/\s+/g, '_')}_${resultId}.pdf"`,
        'Content-Length': pdfBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('Error generating certificate:', error);
    return NextResponse.json(
      { error: 'Failed to generate certificate' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const resultId = params.id;

    // Get test result with candidate info
    const result = await db
      .select({
        testResult: testResults,
        candidate: candidates,
      })
      .from(testResults)
      .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        { error: 'Test result not found' },
        { status: 404 }
      );
    }

    const { testResult, candidate } = result[0];

    // Generate the certificate PDF
    const pdfBase64 = await generateCertificate(testResult, candidate);

    // Update the test result to mark certificate as generated
    await db
      .update(testResults)
      .set({ 
        certificateGenerated: true,
        updatedAt: new Date()
      })
      .where(eq(testResults.id, resultId));

    return NextResponse.json({
      success: true,
      message: 'Certificate generated successfully',
      certificateId: resultId,
      downloadUrl: `/api/certificate/${resultId}`,
    });

  } catch (error) {
    console.error('Error generating certificate:', error);
    return NextResponse.json(
      { error: 'Failed to generate certificate' },
      { status: 500 }
    );
  }
}
