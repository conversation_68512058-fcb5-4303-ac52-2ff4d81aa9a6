import Anthropic from '@anthropic-ai/sdk';
import { TestResult, Candidate } from './db/schema';

// Additional interfaces for the feedback API
export interface AIFeedbackRequest {
  listeningScore?: number;
  readingScore?: number;
  writingScore?: number;
  speakingScore?: number;
  overallScore: number;
}

export interface AIFeedback {
  overallAssessment: string;
  strengths: string[];
  areasForImprovement: string[];
  specificRecommendations: {
    listening: string;
    reading: string;
    writing: string;
    speaking: string;
  };
  studyPlan: string;
  nextSteps: string[];
}

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY!,
});

export interface AIFeedbackData {
  listeningFeedback: string;
  readingFeedback: string;
  writingFeedback: string;
  speakingFeedback: string;
  overallFeedback: string;
  recommendations: string;
  strengths: string[];
  weaknesses: string[];
  studyPlan: {
    timeframe: string;
    focusAreas: string[];
    resources: string[];
    practiceActivities: string[];
  };
}

export async function generateAIFeedback(
  testResult: TestResult,
  candidate: Candidate
): Promise<AIFeedbackData> {
  const prompt = `
You are an expert IELTS examiner and English language learning advisor. Please provide comprehensive feedback for the following IELTS test results:

Candidate Information:
- Name: ${candidate.fullName}
- Nationality: ${candidate.nationality}
- Test Date: ${candidate.testDate}

Test Scores:
- Listening: ${testResult.listeningBandScore}/9.0 (Raw: ${testResult.listeningScore})
- Reading: ${testResult.readingBandScore}/9.0 (Raw: ${testResult.readingScore})
- Writing: ${testResult.writingBandScore}/9.0 (Task 1: ${testResult.writingTask1Score}, Task 2: ${testResult.writingTask2Score})
- Speaking: ${testResult.speakingBandScore}/9.0 (Fluency: ${testResult.speakingFluencyScore}, Lexical: ${testResult.speakingLexicalScore}, Grammar: ${testResult.speakingGrammarScore}, Pronunciation: ${testResult.speakingPronunciationScore})
- Overall Band Score: ${testResult.overallBandScore}/9.0

Please provide:

1. Detailed feedback for each skill (Listening, Reading, Writing, Speaking) - 2-3 sentences each
2. Overall performance feedback - 3-4 sentences
3. Specific recommendations for improvement - 4-5 actionable points
4. List of 3-4 key strengths
5. List of 3-4 areas for improvement
6. A personalized study plan including:
   - Recommended timeframe for improvement
   - 3-4 focus areas
   - 4-5 specific resources
   - 5-6 practice activities

Format your response as JSON with the following structure:
{
  "listeningFeedback": "...",
  "readingFeedback": "...",
  "writingFeedback": "...",
  "speakingFeedback": "...",
  "overallFeedback": "...",
  "recommendations": "...",
  "strengths": ["...", "...", "..."],
  "weaknesses": ["...", "...", "..."],
  "studyPlan": {
    "timeframe": "...",
    "focusAreas": ["...", "...", "..."],
    "resources": ["...", "...", "...", "..."],
    "practiceActivities": ["...", "...", "...", "...", "..."]
  }
}

Make the feedback constructive, specific, and encouraging. Focus on practical advice that the candidate can implement.
`;

  try {
    const response = await anthropic.messages.create({
      model: 'claude-3-sonnet-20240229',
      max_tokens: 2000,
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
    });

    const content = response.content[0];
    if (content.type !== 'text') {
      throw new Error('Unexpected response type from Claude');
    }

    // Parse the JSON response
    const feedbackData = JSON.parse(content.text) as AIFeedbackData;

    return feedbackData;
  } catch (error) {
    console.error('Error generating AI feedback:', error);

    // Return fallback feedback if AI service fails
    return {
      listeningFeedback: `With a band score of ${testResult.listeningBandScore}, your listening skills show good comprehension abilities. Continue practicing with various accents and audio materials.`,
      readingFeedback: `Your reading score of ${testResult.readingBandScore} indicates solid reading comprehension. Focus on improving speed and accuracy with different text types.`,
      writingFeedback: `Your writing band score of ${testResult.writingBandScore} shows competent writing skills. Work on task achievement, coherence, and lexical resource.`,
      speakingFeedback: `With a speaking score of ${testResult.speakingBandScore}, you demonstrate good oral communication. Continue practicing fluency and pronunciation.`,
      overallFeedback: `Your overall band score of ${testResult.overallBandScore} reflects your current English proficiency level. With focused practice, you can improve your performance across all skills.`,
      recommendations: 'Practice regularly with authentic IELTS materials, focus on your weaker skills, and consider taking a preparation course.',
      strengths: ['Good overall comprehension', 'Adequate vocabulary range', 'Basic grammar understanding'],
      weaknesses: ['Need more practice with complex structures', 'Improve accuracy', 'Enhance fluency'],
      studyPlan: {
        timeframe: '3-6 months of focused study',
        focusAreas: ['Grammar accuracy', 'Vocabulary expansion', 'Test strategies'],
        resources: ['Official IELTS materials', 'Cambridge IELTS books', 'Online practice tests', 'English news websites'],
        practiceActivities: ['Daily reading practice', 'Speaking with native speakers', 'Writing essays weekly', 'Listening to podcasts', 'Taking mock tests']
      }
    };
  }
}

// New function for the feedback API
export async function generateAIFeedback(request: AIFeedbackRequest): Promise<AIFeedback> {
  const { overallScore, listeningScore, readingScore, writingScore, speakingScore } = request;

  // Check if Claude API key is available
  const apiKey = process.env.ANTHROPIC_API_KEY;

  if (!apiKey) {
    console.warn('ANTHROPIC_API_KEY not found, using mock feedback');
    return generateMockFeedback(request);
  }

  try {
    // Prepare the prompt for Claude
    const prompt = `You are an expert IELTS examiner and English language teacher. Generate personalized feedback for a student based on their IELTS test scores.

Test Scores:
- Overall Band Score: ${overallScore}
- Listening: ${listeningScore || 'Not provided'}
- Reading: ${readingScore || 'Not provided'}
- Writing: ${writingScore || 'Not provided'}
- Speaking: ${speakingScore || 'Not provided'}

Please provide detailed feedback in the following JSON format:
{
  "overallAssessment": "A comprehensive assessment of the student's English proficiency level",
  "strengths": ["List of 3-4 specific strengths based on the scores"],
  "areasForImprovement": ["List of 3-4 specific areas that need improvement"],
  "specificRecommendations": {
    "listening": "Specific advice for improving listening skills",
    "reading": "Specific advice for improving reading skills",
    "writing": "Specific advice for improving writing skills",
    "speaking": "Specific advice for improving speaking skills"
  },
  "studyPlan": "A detailed study plan recommendation based on the current level",
  "nextSteps": ["List of 4-5 actionable next steps for improvement"]
}

Make the feedback encouraging but honest, specific to the scores provided, and actionable. Consider the IELTS band descriptors when providing recommendations.`;

    const response = await anthropic.messages.create({
      model: 'claude-3-sonnet-20240229',
      max_tokens: 2000,
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
    });

    const content = response.content[0];
    if (content.type !== 'text') {
      throw new Error('Unexpected response type from Claude');
    }

    // Parse the JSON response
    try {
      const feedback = JSON.parse(content.text);
      return feedback;
    } catch (parseError) {
      console.error('Failed to parse Claude response:', content.text);
      throw new Error('Invalid response format from AI service');
    }

  } catch (error) {
    console.error('Claude API error:', error);
    // Fallback to mock feedback if API fails
    return generateMockFeedback(request);
  }
}

function generateMockFeedback(request: AIFeedbackRequest): AIFeedback {
  const { overallScore, listeningScore, readingScore, writingScore, speakingScore } = request;

  // Mock feedback based on score ranges
  if (overallScore >= 7.0) {
    return {
      overallAssessment: "Excellent performance! You have demonstrated strong English proficiency across all skills. Your overall band score of " + overallScore + " indicates you are a competent user of English with good operational command of the language.",
      strengths: [
        "Strong overall command of English language",
        "Good vocabulary range and accuracy in most contexts",
        "Effective communication skills with minor inaccuracies",
        "Ability to handle complex language situations"
      ],
      areasForImprovement: [
        "Fine-tune advanced grammar structures for academic contexts",
        "Expand specialized academic and professional vocabulary",
        "Practice complex sentence formations and cohesive devices",
        "Work on consistency across all four skills"
      ],
      specificRecommendations: {
        listening: "Focus on academic lectures, complex discussions, and various English accents. Practice note-taking while listening to improve comprehension of detailed information.",
        reading: "Practice with academic texts, research papers, and complex argumentative essays. Work on speed reading techniques while maintaining comprehension.",
        writing: "Work on advanced essay structures, sophisticated argumentation, and academic writing conventions. Focus on task achievement and coherence.",
        speaking: "Practice formal presentations, debates, and discussions on abstract topics. Work on fluency and natural expression of complex ideas."
      },
      studyPlan: "Continue with advanced materials focusing on academic English. Dedicate 2-3 hours daily to practice, with emphasis on maintaining consistency across all skills. Use authentic materials like academic journals, TED talks, and formal debates.",
      nextSteps: [
        "Take regular practice tests to maintain performance level",
        "Focus on any weaker skills to achieve balance across all areas",
        "Consider advanced English courses or academic preparation programs",
        "Practice with time constraints to improve efficiency",
        "Engage with native speakers in academic or professional contexts"
      ]
    };
  } else if (overallScore >= 5.5) {
    return {
      overallAssessment: "Good foundation with room for improvement in specific areas. Your overall band score of " + overallScore + " shows you are a modest user of English with partial command of the language, coping with overall meaning in most situations.",
      strengths: [
        "Basic communication skills are well-established",
        "Understanding of fundamental grammar structures",
        "Adequate vocabulary for everyday and familiar situations",
        "Ability to express basic ideas and opinions clearly"
      ],
      areasForImprovement: [
        "Expand vocabulary range for academic and professional contexts",
        "Improve complex grammar usage and sentence structures",
        "Enhance fluency and coherence in extended discourse",
        "Develop better accuracy in language use"
      ],
      specificRecommendations: {
        listening: "Practice with various accents, speeds, and contexts. Focus on understanding main ideas and specific details in academic and social situations.",
        reading: "Work on skimming and scanning techniques. Practice with longer texts and improve vocabulary through extensive reading.",
        writing: "Focus on paragraph structure, linking words, and task response. Practice both formal and informal writing styles with attention to coherence.",
        speaking: "Practice pronunciation, intonation, and natural speech patterns. Work on expressing ideas clearly and developing responses fully."
      },
      studyPlan: "Structured study plan focusing on intermediate to upper-intermediate materials. Dedicate 1-2 hours daily with balanced practice across all four skills. Use IELTS preparation materials and general English improvement resources.",
      nextSteps: [
        "Daily practice with all four skills using varied materials",
        "Join English conversation groups or language exchange programs",
        "Use IELTS preparation books and online resources systematically",
        "Focus on building vocabulary through reading and listening",
        "Take regular practice tests to track improvement"
      ]
    };
  } else {
    return {
      overallAssessment: "Foundation level with significant room for improvement across all skills. Your overall band score of " + overallScore + " indicates limited user level, with basic competence limited to familiar situations and frequent communication breakdowns.",
      strengths: [
        "Basic understanding of English structure and patterns",
        "Willingness to communicate despite limitations",
        "Some vocabulary knowledge for familiar topics",
        "Ability to convey basic information in simple situations"
      ],
      areasForImprovement: [
        "Build fundamental vocabulary for everyday situations",
        "Strengthen basic grammar and sentence construction",
        "Improve listening comprehension for simple conversations",
        "Develop basic writing skills and paragraph organization"
      ],
      specificRecommendations: {
        listening: "Start with simple conversations, basic instructions, and familiar topics. Use visual aids and context clues to support understanding.",
        reading: "Begin with short, simple texts on familiar topics. Focus on building sight vocabulary and basic comprehension skills.",
        writing: "Focus on basic sentence structure, simple paragraphs, and essential grammar patterns. Practice writing about familiar topics.",
        speaking: "Practice basic conversations, pronunciation of common words, and expressing simple ideas clearly and confidently."
      },
      studyPlan: "Intensive foundation course focusing on basic English skills. Dedicate 1-2 hours daily to structured learning with emphasis on building confidence and fundamental skills. Use beginner-level materials and seek guidance from qualified teachers.",
      nextSteps: [
        "Enroll in a basic English course with qualified instruction",
        "Practice daily with simple, structured materials",
        "Focus on building confidence through successful communication",
        "Use visual and audio aids to support learning",
        "Set small, achievable goals to maintain motivation"
      ]
    };
  }
}
