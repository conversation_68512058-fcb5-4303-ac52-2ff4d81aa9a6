'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, FileText, Download, ArrowLeft } from 'lucide-react';

export default function SearchPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState<'name' | 'email' | 'passport' | 'certificate'>('name');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setIsLoading(true);
    setHasSearched(true);

    try {
      const response = await fetch('/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: searchQuery,
          searchType,
        }),
      });

      if (response.ok) {
        const results = await response.json();
        setSearchResults(results);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  const downloadCertificate = async (resultId: string) => {
    try {
      const response = await fetch(`/api/certificate/${resultId}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `IELTS_Certificate_${resultId}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Download error:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="flex items-center text-blue-600 hover:text-blue-700 mr-4">
                <ArrowLeft className="h-5 w-5 mr-1" />
                Back
              </Link>
              <FileText className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">Search IELTS Results</h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Search Form */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Find Your Test Results</h2>
          
          <form onSubmit={handleSearch} className="space-y-6">
            <div>
              <label htmlFor="searchType" className="block text-sm font-medium text-gray-700 mb-2">
                Search by
              </label>
              <select
                id="searchType"
                value={searchType}
                onChange={(e) => setSearchType(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="name">Full Name</option>
                <option value="email">Email Address</option>
                <option value="passport">Passport Number</option>
                <option value="certificate">Certificate ID</option>
              </select>
            </div>

            <div>
              <label htmlFor="searchQuery" className="block text-sm font-medium text-gray-700 mb-2">
                {searchType === 'name' && 'Enter your full name'}
                {searchType === 'email' && 'Enter your email address'}
                {searchType === 'passport' && 'Enter your passport number'}
                {searchType === 'certificate' && 'Enter your certificate ID'}
              </label>
              <input
                type="text"
                id="searchQuery"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={
                  searchType === 'name' ? 'John Doe' :
                  searchType === 'email' ? '<EMAIL>' :
                  searchType === 'passport' ? 'A12345678' :
                  'CERT123456'
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Searching...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Search Results
                </>
              )}
            </button>
          </form>
        </div>

        {/* Search Results */}
        {hasSearched && (
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h3 className="text-xl font-bold text-gray-900 mb-6">Search Results</h3>
            
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Searching...</p>
              </div>
            ) : searchResults.length > 0 ? (
              <div className="space-y-6">
                {searchResults.map((result) => (
                  <div key={result.id} className="border border-gray-200 rounded-lg p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="text-lg font-semibold text-gray-900">{result.candidate.fullName}</h4>
                        <p className="text-gray-600">Test Date: {new Date(result.candidate.testDate).toLocaleDateString()}</p>
                        <p className="text-gray-600">Test Center: {result.candidate.testCenter}</p>
                        
                        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div className="text-center">
                            <p className="text-sm text-gray-500">Listening</p>
                            <p className="text-lg font-semibold text-blue-600">{result.listeningBandScore || 'N/A'}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-500">Reading</p>
                            <p className="text-lg font-semibold text-blue-600">{result.readingBandScore || 'N/A'}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-500">Writing</p>
                            <p className="text-lg font-semibold text-blue-600">{result.writingBandScore || 'N/A'}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-500">Speaking</p>
                            <p className="text-lg font-semibold text-blue-600">{result.speakingBandScore || 'N/A'}</p>
                          </div>
                        </div>
                        
                        <div className="mt-4 text-center">
                          <p className="text-sm text-gray-500">Overall Band Score</p>
                          <p className="text-2xl font-bold text-green-600">{result.overallBandScore || 'N/A'}</p>
                        </div>
                      </div>
                      
                      {result.certificateGenerated && (
                        <button
                          onClick={() => downloadCertificate(result.id)}
                          className="ml-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download Certificate
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No results found. Please check your search criteria and try again.</p>
              </div>
            )}
          </div>
        )}
      </main>
    </div>
  );
}
