import jsPDF from 'jspdf';
import { TestResult, Candidate } from './db/schema';
import { formatDate, formatBandScore, getBandScoreDescription } from './utils';

export async function generateCertificate(
  testResult: TestResult,
  candidate: Candidate
): Promise<string> {
  const pdf = new jsPDF({
    orientation: 'landscape',
    unit: 'mm',
    format: 'a4'
  });

  // Set up the certificate design
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  
  // Background and border
  pdf.setFillColor(245, 245, 245);
  pdf.rect(0, 0, pageWidth, pageHeight, 'F');
  
  pdf.setDrawColor(0, 0, 0);
  pdf.setLineWidth(2);
  pdf.rect(10, 10, pageWidth - 20, pageHeight - 20);
  
  pdf.setLineWidth(1);
  pdf.rect(15, 15, pageWidth - 30, pageHeight - 30);

  // Header
  pdf.setFontSize(28);
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(0, 0, 0);
  pdf.text('IELTS TEST REPORT FORM', pageWidth / 2, 35, { align: 'center' });

  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'normal');
  pdf.text('International English Language Testing System', pageWidth / 2, 45, { align: 'center' });

  // Candidate Information
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'bold');
  pdf.text('CANDIDATE DETAILS', 25, 65);
  
  pdf.setFont('helvetica', 'normal');
  pdf.text(`Name: ${candidate.fullName}`, 25, 75);
  pdf.text(`Date of Birth: ${formatDate(candidate.dateOfBirth)}`, 25, 85);
  pdf.text(`Nationality: ${candidate.nationality}`, 25, 95);
  pdf.text(`Passport Number: ${candidate.passportNumber}`, 25, 105);
  pdf.text(`Test Date: ${formatDate(candidate.testDate)}`, 25, 115);
  pdf.text(`Test Centre: ${candidate.testCenter}`, 25, 125);

  // Test Results Table
  pdf.setFont('helvetica', 'bold');
  pdf.text('TEST RESULTS', 25, 145);

  // Table headers
  const tableStartY = 155;
  const colWidths = [60, 40, 40, 80];
  const colPositions = [25, 85, 125, 165];

  pdf.setFillColor(230, 230, 230);
  pdf.rect(25, tableStartY, colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3], 10, 'F');
  
  pdf.setFont('helvetica', 'bold');
  pdf.text('Skill', colPositions[0] + 2, tableStartY + 7);
  pdf.text('Raw Score', colPositions[1] + 2, tableStartY + 7);
  pdf.text('Band Score', colPositions[2] + 2, tableStartY + 7);
  pdf.text('Description', colPositions[3] + 2, tableStartY + 7);

  // Table rows
  const skills = [
    {
      name: 'Listening',
      raw: testResult.listeningScore?.toString() || 'N/A',
      band: formatBandScore(Number(testResult.listeningBandScore) || 0),
      description: getBandScoreDescription(Number(testResult.listeningBandScore) || 0)
    },
    {
      name: 'Reading',
      raw: testResult.readingScore?.toString() || 'N/A',
      band: formatBandScore(Number(testResult.readingBandScore) || 0),
      description: getBandScoreDescription(Number(testResult.readingBandScore) || 0)
    },
    {
      name: 'Writing',
      raw: `T1: ${testResult.writingTask1Score || 'N/A'}, T2: ${testResult.writingTask2Score || 'N/A'}`,
      band: formatBandScore(Number(testResult.writingBandScore) || 0),
      description: getBandScoreDescription(Number(testResult.writingBandScore) || 0)
    },
    {
      name: 'Speaking',
      raw: 'Assessed',
      band: formatBandScore(Number(testResult.speakingBandScore) || 0),
      description: getBandScoreDescription(Number(testResult.speakingBandScore) || 0)
    }
  ];

  pdf.setFont('helvetica', 'normal');
  skills.forEach((skill, index) => {
    const rowY = tableStartY + 15 + (index * 10);
    
    // Alternate row colors
    if (index % 2 === 1) {
      pdf.setFillColor(248, 248, 248);
      pdf.rect(25, rowY - 3, colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3], 10, 'F');
    }
    
    pdf.text(skill.name, colPositions[0] + 2, rowY + 4);
    pdf.text(skill.raw, colPositions[1] + 2, rowY + 4);
    pdf.text(skill.band, colPositions[2] + 2, rowY + 4);
    pdf.text(skill.description, colPositions[3] + 2, rowY + 4);
  });

  // Overall Band Score
  const overallY = tableStartY + 70;
  pdf.setFillColor(220, 220, 220);
  pdf.rect(25, overallY, colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3], 12, 'F');
  
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(14);
  pdf.text('OVERALL BAND SCORE', colPositions[0] + 2, overallY + 8);
  pdf.text(formatBandScore(Number(testResult.overallBandScore) || 0), colPositions[2] + 2, overallY + 8);
  pdf.text(getBandScoreDescription(Number(testResult.overallBandScore) || 0), colPositions[3] + 2, overallY + 8);

  // Footer
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');
  pdf.text('This Test Report Form is valid for two years from the test date.', pageWidth / 2, pageHeight - 30, { align: 'center' });
  pdf.text(`Certificate ID: ${testResult.id}`, pageWidth / 2, pageHeight - 20, { align: 'center' });
  pdf.text(`Generated on: ${formatDate(new Date())}`, pageWidth / 2, pageHeight - 10, { align: 'center' });

  // Convert to base64 string
  const pdfBase64 = pdf.output('datauristring');
  
  return pdfBase64;
}
