import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates } from '@/lib/db/schema';
import { ilike, or, desc, count } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';

    const offset = (page - 1) * limit;

    // Build search conditions
    const searchConditions = search
      ? or(
          ilike(candidates.fullName, `%${search}%`),
          ilike(candidates.email, `%${search}%`),
          ilike(candidates.passportNumber, `%${search}%`)
        )
      : undefined;

    // Get total count
    const totalResult = await db
      .select({ count: count() })
      .from(candidates)
      .where(searchConditions);
    
    const total = totalResult[0]?.count || 0;

    // Get candidates
    const candidatesList = await db
      .select()
      .from(candidates)
      .where(searchConditions)
      .orderBy(desc(candidates.createdAt))
      .limit(limit)
      .offset(offset);

    return NextResponse.json({
      candidates: candidatesList,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    });
  } catch (error) {
    console.error('Error fetching candidates:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // Validate required fields
    const requiredFields = [
      'fullName',
      'email',
      'phoneNumber',
      'dateOfBirth',
      'nationality',
      'passportNumber',
      'testDate',
      'testCenter'
    ];

    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Create candidate
    const newCandidate = await db
      .insert(candidates)
      .values({
        fullName: data.fullName,
        email: data.email,
        phoneNumber: data.phoneNumber,
        dateOfBirth: new Date(data.dateOfBirth),
        nationality: data.nationality,
        passportNumber: data.passportNumber,
        testDate: new Date(data.testDate),
        testCenter: data.testCenter,
        photoUrl: data.photoUrl,
      })
      .returning();

    return NextResponse.json(newCandidate[0], { status: 201 });
  } catch (error) {
    console.error('Error creating candidate:', error);
    
    // Handle unique constraint violations
    if (error instanceof Error && error.message.includes('unique')) {
      if (error.message.includes('email')) {
        return NextResponse.json(
          { error: 'Email address already exists' },
          { status: 409 }
        );
      }
      if (error.message.includes('passport')) {
        return NextResponse.json(
          { error: 'Passport number already exists' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
