import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { generateAIFeedback } from '@/lib/ai-service';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { resultId, scores } = await request.json();

    if (!resultId || !scores) {
      return NextResponse.json(
        { error: 'Result ID and scores are required' },
        { status: 400 }
      );
    }

    // Validate scores
    const { listening, reading, writing, speaking, overall } = scores;
    
    if (!overall) {
      return NextResponse.json(
        { error: 'Overall band score is required for feedback generation' },
        { status: 400 }
      );
    }

    // Generate AI feedback
    const feedback = await generateAIFeedback({
      listeningScore: listening,
      readingScore: reading,
      writingScore: writing,
      speakingScore: speaking,
      overallScore: overall,
    });

    return NextResponse.json({
      feedback,
      resultId,
    });
  } catch (error) {
    console.error('Error generating AI feedback:', error);
    
    // Handle specific AI service errors
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'AI service configuration error. Please contact administrator.' },
          { status: 500 }
        );
      }
      if (error.message.includes('rate limit')) {
        return NextResponse.json(
          { error: 'AI service temporarily unavailable. Please try again later.' },
          { status: 429 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to generate feedback. Please try again.' },
      { status: 500 }
    );
  }
}
