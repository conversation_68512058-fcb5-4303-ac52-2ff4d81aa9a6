import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testResults } from '@/lib/db/schema';
import { eq, desc, count } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user?.id;

    // Get total results entered by this checker
    const totalResultsResult = await db
      .select({ count: count() })
      .from(testResults)
      .where(eq(testResults.enteredBy, userId));
    const totalResultsEntered = totalResultsResult[0]?.count || 0;

    // Get pending results entered by this checker
    const pendingResultsResult = await db
      .select({ count: count() })
      .from(testResults)
      .where(eq(testResults.enteredBy, userId))
      .where(eq(testResults.status, 'pending'));
    const pendingResults = pendingResultsResult[0]?.count || 0;

    // Get completed results entered by this checker
    const completedResultsResult = await db
      .select({ count: count() })
      .from(testResults)
      .where(eq(testResults.enteredBy, userId))
      .where(eq(testResults.status, 'completed'));
    const completedResults = completedResultsResult[0]?.count || 0;

    // Get recent results entered by this checker
    const recentResults = await db
      .select({
        id: testResults.id,
        overallBandScore: testResults.overallBandScore,
        status: testResults.status,
        createdAt: testResults.createdAt,
        candidate: {
          fullName: candidates.fullName,
        },
      })
      .from(testResults)
      .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
      .where(eq(testResults.enteredBy, userId))
      .orderBy(desc(testResults.createdAt))
      .limit(10);

    return NextResponse.json({
      totalResultsEntered,
      pendingResults,
      completedResults,
      recentResults,
    });
  } catch (error) {
    console.error('Checker dashboard stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
