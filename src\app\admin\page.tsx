'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Users, 
  FileText, 
  TrendingUp, 
  Clock,
  UserPlus,
  BarChart3,
  Search,
  Download
} from 'lucide-react';

interface DashboardStats {
  totalCandidates: number;
  totalResults: number;
  pendingResults: number;
  completedResults: number;
  recentCandidates: any[];
  recentResults: any[];
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalCandidates: 0,
    totalResults: 0,
    pendingResults: 0,
    completedResults: 0,
    recentCandidates: [],
    recentResults: [],
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('/api/admin/dashboard');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const statCards = [
    {
      name: 'Total Candidates',
      value: stats.totalCandidates,
      icon: Users,
      color: 'bg-blue-500',
      href: '/admin/candidates',
    },
    {
      name: 'Test Results',
      value: stats.totalResults,
      icon: FileText,
      color: 'bg-green-500',
      href: '/admin/results',
    },
    {
      name: 'Pending Results',
      value: stats.pendingResults,
      icon: Clock,
      color: 'bg-yellow-500',
      href: '/admin/results?status=pending',
    },
    {
      name: 'Completed Results',
      value: stats.completedResults,
      icon: TrendingUp,
      color: 'bg-purple-500',
      href: '/admin/results?status=completed',
    },
  ];

  const quickActions = [
    {
      name: 'Add New Candidate',
      description: 'Register a new test candidate',
      href: '/admin/candidates/new',
      icon: UserPlus,
      color: 'bg-blue-600 hover:bg-blue-700',
    },
    {
      name: 'View All Results',
      description: 'Browse all test results',
      href: '/admin/results',
      icon: BarChart3,
      color: 'bg-green-600 hover:bg-green-700',
    },
    {
      name: 'Search System',
      description: 'Search candidates and results',
      href: '/admin/search',
      icon: Search,
      color: 'bg-purple-600 hover:bg-purple-700',
    },
    {
      name: 'Export Data',
      description: 'Download system reports',
      href: '/admin/export',
      icon: Download,
      color: 'bg-gray-600 hover:bg-gray-700',
    },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
        <p className="text-gray-600">Overview of the IELTS Certification System</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat) => {
          const Icon = stat.icon;
          return (
            <Link
              key={stat.name}
              href={stat.href}
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`${stat.color} p-3 rounded-md`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {stat.name}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stat.value.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action) => {
            const Icon = action.icon;
            return (
              <Link
                key={action.name}
                href={action.href}
                className={`${action.color} text-white p-6 rounded-lg shadow hover:shadow-md transition-all`}
              >
                <div className="flex items-center">
                  <Icon className="h-8 w-8 mr-4" />
                  <div>
                    <h3 className="font-medium">{action.name}</h3>
                    <p className="text-sm opacity-90">{action.description}</p>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Candidates */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Recent Candidates</h3>
          </div>
          <div className="p-6">
            {stats.recentCandidates.length > 0 ? (
              <div className="space-y-4">
                {stats.recentCandidates.slice(0, 5).map((candidate) => (
                  <div key={candidate.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">{candidate.fullName}</p>
                      <p className="text-sm text-gray-500">{candidate.email}</p>
                    </div>
                    <div className="text-sm text-gray-500">
                      {new Date(candidate.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No recent candidates</p>
            )}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <Link
                href="/admin/candidates"
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                View all candidates →
              </Link>
            </div>
          </div>
        </div>

        {/* Recent Results */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Recent Test Results</h3>
          </div>
          <div className="p-6">
            {stats.recentResults.length > 0 ? (
              <div className="space-y-4">
                {stats.recentResults.slice(0, 5).map((result) => (
                  <div key={result.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">{result.candidate?.fullName}</p>
                      <p className="text-sm text-gray-500">
                        Overall: {result.overallBandScore || 'Pending'}
                      </p>
                    </div>
                    <div className="text-sm text-gray-500">
                      {new Date(result.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No recent results</p>
            )}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <Link
                href="/admin/results"
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                View all results →
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
